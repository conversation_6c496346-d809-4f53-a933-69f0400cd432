name: Playwright Tests
on:
    push:
        branches: [main, master]
    pull_request:
        branches: [main, master]
jobs:
    test:
        timeout-minutes: 60
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4
            - uses: actions/setup-node@v4
              with:
                  node-version: lts/*
            - name: Install dependencies
              run: npm ci
            - name: Install Playwright Browsers
              run: npx playwright install --with-deps
            - name: Run Playwright tests
              run: npx playwright test
            - uses: actions/upload-artifact@v4
              if: ${{ !cancelled() }}
              with:
                  name: playwright-report
                  path: playwright-report/
                  retention-days: 30
            - name: '📎 Info: How to download the HTML report'
              if: always()
              run: |
                  echo "✅ Test execution completed."
                  echo "➡️ Download the HTML report from the 'Artifacts' section above."
                  echo "📂 Unzip it and open 'index.html' in your browser to view the results."
