{"name": "bsa-2025-checkly-automation-test", "version": "1.0.0", "description": "Automated API and UI tests for the Checkly project (Binary Studio Academy 2025)", "main": "index.js", "scripts": {"test": "npx playwright test", "test:ui": "npx playwright test --ui", "report": "npx playwright show-report"}, "repository": {"type": "git", "url": "https://github.com/VictorVagabculov/BSA-2025-Checkly-Automation-Test.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/VictorVagabculov/BSA-2025-Checkly-Automation-Test/issues"}, "homepage": "https://github.com/VictorVagabculov/BSA-2025-Checkly-Automation-Test#readme", "type": "commonjs", "devDependencies": {"@playwright/test": "^1.54.1", "@types/node": "^24.1.0"}, "dependencies": {"@faker-js/faker": "^9.9.0", "ajv": "^8.17.1", "prettier": "^3.6.2"}, "engines": {"node": ">=16.0.0"}, "private": true}