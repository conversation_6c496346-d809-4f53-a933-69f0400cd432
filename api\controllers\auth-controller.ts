import { APIRequestContext, APIResponse } from "@playwright/test";

export class AuthController {
    constructor(private requestContext: APIRequestContext) {}

    async login(email: string, password: string): Promise<APIResponse> {
        const response = await this.requestContext.post("/api/login", {
            data: {
                email,
                password,
            },
        });

        return response;
    }

    async register(email: string, name: string, password: string): Promise<APIResponse> {
        const response = await this.requestContext.post("/api/register", {
            data: {
                email,
                name,
                password,
            },
        });

        return response;
    }
}