export const registerResponseSchema = {
    type: 'object',
    properties: {
        user: { type: 'object', properties: {
            email: { type: 'string' },
            password: { type: 'string' },
            name: { type: 'string' },
        }},
        token: { type: 'string' },
    },
    required: ['user', 'token'],
}

export const loginResponseSchema = {
    type: 'object',
    properties: {
        user: { type: 'object', properties: {
            email: { type: 'string' },
            password: { type: 'string' },
        }},
        token: { type: 'string' },
    },
    required: ['user', 'token'],
}